import { Product } from '@/types/types';

export interface IngredientUsage {
  ingredientName: string;
  productId: string;
  productName: string;
  quantityUsed: number;
  unit: string;
  productStock: number;
}

export interface IngredientStockInfo {
  ingredientName: string;
  totalAvailableStock: number;
  totalReservedStock: number;
  actualAvailableStock: number;
  usageBreakdown: IngredientUsage[];
  matchingProducts: Product[];
}

export interface RecipeIngredientWithStock {
  name: string;
  quantity: string;
  unit: string;
  productId?: string;
  requiredQuantity: number;
  availableStock: number;
  reservedStock: number;
  actualAvailableStock: number;
  canFulfill: boolean;
  stockShortage: number;
}

/**
 * Parse ingredient quantity from string format (e.g., "30", "30ml", "1.5")
 */
export function parseIngredientQuantity(quantityStr: string): number {
  const numericValue = parseFloat(quantityStr.replace(/[^\d.-]/g, ''));
  return isNaN(numericValue) ? 0 : numericValue;
}

/**
 * Find all products that match an ingredient name
 */
export function findMatchingProducts(ingredientName: string, products: Product[]): Product[] {
  const searchTerm = ingredientName.toLowerCase().trim();
  
  return products.filter(product => {
    const productName = product.name.toLowerCase();
    
    // Exact match gets highest priority
    if (productName === searchTerm) return true;
    
    // Check if product name contains the ingredient name
    if (productName.includes(searchTerm)) return true;
    
    // Check if ingredient name contains the product name (for cases like "Gin" matching "Gin Beefeater")
    if (searchTerm.includes(productName)) return true;
    
    return false;
  }).sort((a, b) => {
    // Sort by relevance: exact matches first, then by name similarity
    const aName = a.name.toLowerCase();
    const bName = b.name.toLowerCase();
    
    if (aName === searchTerm) return -1;
    if (bName === searchTerm) return 1;
    
    return aName.localeCompare(bName);
  });
}

/**
 * Calculate how much of an ingredient is reserved by existing products with recipes
 */
export function calculateReservedStock(ingredientName: string, products: Product[], excludeProductId?: string): number {
  let totalReserved = 0;
  
  products.forEach(product => {
    // Skip the product we're currently creating/editing
    if (excludeProductId && product.id === excludeProductId) return;
    
    // Only consider products with recipes that have stock
    if (!product.has_recipe || !product.ingredients || product.stock <= 0) return;
    
    try {
      const ingredients = JSON.parse(product.ingredients);
      
      ingredients.forEach((ingredient: any) => {
        // Check if this ingredient matches our search
        if (ingredient.name.toLowerCase().includes(ingredientName.toLowerCase()) ||
            ingredientName.toLowerCase().includes(ingredient.name.toLowerCase())) {
          
          const quantityPerUnit = parseIngredientQuantity(ingredient.quantity);
          const reservedAmount = quantityPerUnit * product.stock;
          totalReserved += reservedAmount;
        }
      });
    } catch (error) {
      console.warn(`Failed to parse ingredients for product ${product.name}:`, error);
    }
  });
  
  return totalReserved;
}

/**
 * Get comprehensive stock information for an ingredient
 */
export function getIngredientStockInfo(ingredientName: string, products: Product[], excludeProductId?: string): IngredientStockInfo {
  const matchingProducts = findMatchingProducts(ingredientName, products);
  const totalAvailableStock = matchingProducts.reduce((sum, product) => sum + product.stock, 0);
  const totalReservedStock = calculateReservedStock(ingredientName, products, excludeProductId);
  const actualAvailableStock = Math.max(0, totalAvailableStock - totalReservedStock);
  
  // Create usage breakdown
  const usageBreakdown: IngredientUsage[] = [];
  
  products.forEach(product => {
    if (excludeProductId && product.id === excludeProductId) return;
    if (!product.has_recipe || !product.ingredients || product.stock <= 0) return;
    
    try {
      const ingredients = JSON.parse(product.ingredients);
      
      ingredients.forEach((ingredient: any) => {
        if (ingredient.name.toLowerCase().includes(ingredientName.toLowerCase()) ||
            ingredientName.toLowerCase().includes(ingredient.name.toLowerCase())) {
          
          const quantityPerUnit = parseIngredientQuantity(ingredient.quantity);
          
          usageBreakdown.push({
            ingredientName: ingredient.name,
            productId: product.id,
            productName: product.name,
            quantityUsed: quantityPerUnit,
            unit: ingredient.unit || 'ml',
            productStock: product.stock
          });
        }
      });
    } catch (error) {
      console.warn(`Failed to parse ingredients for product ${product.name}:`, error);
    }
  });
  
  return {
    ingredientName,
    totalAvailableStock,
    totalReservedStock,
    actualAvailableStock,
    usageBreakdown,
    matchingProducts
  };
}

/**
 * Calculate stock information for recipe ingredients
 */
export function calculateRecipeIngredientsStock(
  ingredients: Array<{ name: string; quantity: string; unit: string; productId?: string }>,
  products: Product[],
  recipeQuantity: number = 1,
  excludeProductId?: string
): RecipeIngredientWithStock[] {
  
  return ingredients.map(ingredient => {
    const stockInfo = getIngredientStockInfo(ingredient.name, products, excludeProductId);
    const requiredQuantity = parseIngredientQuantity(ingredient.quantity) * recipeQuantity;
    const canFulfill = stockInfo.actualAvailableStock >= requiredQuantity;
    const stockShortage = Math.max(0, requiredQuantity - stockInfo.actualAvailableStock);
    
    return {
      name: ingredient.name,
      quantity: ingredient.quantity,
      unit: ingredient.unit,
      productId: ingredient.productId || stockInfo.matchingProducts[0]?.id,
      requiredQuantity,
      availableStock: stockInfo.totalAvailableStock,
      reservedStock: stockInfo.totalReservedStock,
      actualAvailableStock: stockInfo.actualAvailableStock,
      canFulfill,
      stockShortage
    };
  });
}

/**
 * Validate if a recipe can be created with current stock levels
 */
export function validateRecipeStock(
  ingredients: Array<{ name: string; quantity: string; unit: string }>,
  products: Product[],
  recipeQuantity: number = 1,
  excludeProductId?: string
): { isValid: boolean; errors: string[]; warnings: string[] } {
  
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const ingredientsWithStock = calculateRecipeIngredientsStock(
    ingredients, 
    products, 
    recipeQuantity, 
    excludeProductId
  );
  
  ingredientsWithStock.forEach(ingredient => {
    if (!ingredient.canFulfill) {
      errors.push(
        `Insufficient stock for ${ingredient.name}: need ${ingredient.requiredQuantity}${ingredient.unit}, ` +
        `but only ${ingredient.actualAvailableStock}${ingredient.unit} available after reservations`
      );
    }
    
    if (ingredient.reservedStock > 0) {
      warnings.push(
        `${ingredient.name} has ${ingredient.reservedStock}${ingredient.unit} reserved for other recipes`
      );
    }
    
    if (ingredient.actualAvailableStock < ingredient.requiredQuantity * 2) {
      warnings.push(
        `Low stock warning for ${ingredient.name}: only ${ingredient.actualAvailableStock}${ingredient.unit} available`
      );
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}
