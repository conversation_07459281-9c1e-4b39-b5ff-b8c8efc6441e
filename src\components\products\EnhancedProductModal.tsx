import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertTriangle, CheckCircle } from 'lucide-react';
import { Product, Recipe } from '@/types/types';
import { RecipeIngredientManager } from '@/components/recipe/RecipeIngredientManager';
import { validateRecipeStock } from '@/lib/stockCalculations';
import ImageUpload from '@/app/(components)/image-upload';
import { categoryList } from '@/lib/utils';

interface EnhancedProductModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onProductCreate: (productData: any) => Promise<void>;
  products: Product[];
  recipes: Recipe[];
  isLoading?: boolean;
}

export function EnhancedProductModal({
  open,
  onOpenChange,
  onProductCreate,
  products,
  recipes,
  isLoading = false
}: EnhancedProductModalProps) {
  const [productData, setProductData] = useState({
    name: '',
    description: '',
    category: '',
    stock: 0,
    purchase_price: 0,
    sale_price: 0,
    has_recipe: false
  });

  const [recipeMode, setRecipeMode] = useState<'none' | 'existing' | 'custom'>('none');
  const [selectedRecipeId, setSelectedRecipeId] = useState<string>('');
  const [customIngredients, setCustomIngredients] = useState<Array<{ name: string; quantity: string; unit: string; productId?: string }>>([]);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [stockValidation, setStockValidation] = useState<{ isValid: boolean; errors: string[]; warnings: string[] }>({
    isValid: true,
    errors: [],
    warnings: []
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!open) {
      setProductData({
        name: '',
        description: '',
        category: '',
        stock: 0,
        purchase_price: 0,
        sale_price: 0,
        has_recipe: false
      });
      setRecipeMode('none');
      setSelectedRecipeId('');
      setCustomIngredients([]);
      setImageFile(null);
      setStockValidation({ isValid: true, errors: [], warnings: [] });
    }
  }, [open]);

  // Update stock validation when ingredients or stock change
  useEffect(() => {
    if (recipeMode === 'custom' && customIngredients.length > 0) {
      const validation = validateRecipeStock(customIngredients, products, productData.stock || 1);
      setStockValidation(validation);
    } else if (recipeMode === 'existing' && selectedRecipeId) {
      const selectedRecipe = recipes.find(r => r.id.toString() === selectedRecipeId);
      if (selectedRecipe && selectedRecipe.ingredients) {
        const validation = validateRecipeStock(selectedRecipe.ingredients, products, productData.stock || 1);
        setStockValidation(validation);
      }
    } else {
      setStockValidation({ isValid: true, errors: [], warnings: [] });
    }
  }, [customIngredients, selectedRecipeId, productData.stock, products, recipes, recipeMode]);

  const handleInputChange = (field: string, value: any) => {
    setProductData(prev => ({ ...prev, [field]: value }));
  };

  const handleRecipeModeChange = (mode: 'none' | 'existing' | 'custom') => {
    setRecipeMode(mode);
    setProductData(prev => ({ ...prev, has_recipe: mode !== 'none' }));
    
    if (mode === 'none') {
      setSelectedRecipeId('');
      setCustomIngredients([]);
    }
  };

  const handleSubmit = async () => {
    try {
      // Prepare ingredients data based on recipe mode
      let ingredientsData = null;
      
      if (recipeMode === 'existing' && selectedRecipeId) {
        const selectedRecipe = recipes.find(r => r.id.toString() === selectedRecipeId);
        if (selectedRecipe && selectedRecipe.ingredients) {
          ingredientsData = selectedRecipe.ingredients;
        }
      } else if (recipeMode === 'custom' && customIngredients.length > 0) {
        ingredientsData = customIngredients;
      }

      // Validate stock if recipe is used
      if (ingredientsData && !stockValidation.isValid) {
        throw new Error(`Stock insuficiente:\n${stockValidation.errors.join('\n')}`);
      }

      const finalProductData = {
        ...productData,
        has_recipe: recipeMode !== 'none',
        ingredients: ingredientsData ? JSON.stringify(ingredientsData) : null,
        imageFile
      };

      await onProductCreate(finalProductData);
      onOpenChange(false);
    } catch (error) {
      console.error('Error creating product:', error);
      // Error handling is done by the parent component
    }
  };

  const canSubmit = productData.name && productData.category && stockValidation.isValid;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Crear Nuevo Producto</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Product Information */}
          <Card>
            <CardHeader>
              <CardTitle>Información Básica</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="product-name">Nombre del Producto *</Label>
                  <Input
                    id="product-name"
                    value={productData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Nombre del producto"
                  />
                </div>
                
                <div>
                  <Label htmlFor="product-category">Categoría *</Label>
                  <Select
                    value={productData.category}
                    onValueChange={(value) => handleInputChange('category', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar categoría" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryList.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="product-description">Descripción</Label>
                <Textarea
                  id="product-description"
                  value={productData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Descripción del producto"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="product-stock">Stock Inicial</Label>
                  <Input
                    id="product-stock"
                    type="number"
                    value={productData.stock}
                    onChange={(e) => handleInputChange('stock', parseInt(e.target.value) || 0)}
                    min="0"
                  />
                </div>
                
                <div>
                  <Label htmlFor="purchase-price">Precio de Compra</Label>
                  <Input
                    id="purchase-price"
                    type="number"
                    value={productData.purchase_price}
                    onChange={(e) => handleInputChange('purchase_price', parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                  />
                </div>
                
                <div>
                  <Label htmlFor="sale-price">Precio de Venta</Label>
                  <Input
                    id="sale-price"
                    type="number"
                    value={productData.sale_price}
                    onChange={(e) => handleInputChange('sale_price', parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>

              <div>
                <Label>Imagen del Producto</Label>
                <ImageUpload
                  onImageSelect={setImageFile}
                  currentImageUrl=""
                />
              </div>
            </CardContent>
          </Card>

          {/* Recipe Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Configuración de Receta</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={recipeMode} onValueChange={handleRecipeModeChange}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="none">Sin Receta</TabsTrigger>
                  <TabsTrigger value="existing">Receta Existente</TabsTrigger>
                  <TabsTrigger value="custom">Receta Personalizada</TabsTrigger>
                </TabsList>

                <TabsContent value="none" className="mt-4">
                  <p className="text-gray-600">Este producto no tendrá receta asociada.</p>
                </TabsContent>

                <TabsContent value="existing" className="mt-4">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="existing-recipe">Seleccionar Receta</Label>
                      <Select
                        value={selectedRecipeId}
                        onValueChange={setSelectedRecipeId}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar una receta existente" />
                        </SelectTrigger>
                        <SelectContent>
                          {recipes.map(recipe => (
                            <SelectItem key={recipe.id} value={recipe.id.toString()}>
                              {recipe.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {selectedRecipeId && (
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-medium mb-2">Ingredientes de la receta:</h4>
                        {(() => {
                          const selectedRecipe = recipes.find(r => r.id.toString() === selectedRecipeId);
                          return selectedRecipe?.ingredients?.map((ingredient, index) => (
                            <div key={index} className="flex justify-between items-center py-1">
                              <span>{ingredient.name}</span>
                              <Badge variant="outline">{ingredient.quantity} {ingredient.unit}</Badge>
                            </div>
                          ));
                        })()}
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="custom" className="mt-4">
                  <RecipeIngredientManager
                    ingredients={customIngredients}
                    onIngredientsChange={setCustomIngredients}
                    products={products}
                    recipeQuantity={productData.stock || 1}
                    showStockValidation={true}
                  />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Stock Validation Alerts */}
          {!stockValidation.isValid && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Errores de stock:</div>
                <ul className="list-disc list-inside space-y-1">
                  {stockValidation.errors.map((error, index) => (
                    <li key={index} className="text-sm">{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {stockValidation.warnings.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Advertencias:</div>
                <ul className="list-disc list-inside space-y-1">
                  {stockValidation.warnings.map((warning, index) => (
                    <li key={index} className="text-sm">{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit || isLoading}
            >
              {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Crear Producto
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
