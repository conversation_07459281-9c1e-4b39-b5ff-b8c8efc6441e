import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Settings, BarChart3 } from 'lucide-react';
import { Product, Recipe } from '@/types/types';
import { EnhancedProductModal } from '@/components/products/EnhancedProductModal';
import { RecipeEditor } from '@/components/recipe/RecipeEditor';
import { StockDashboard } from './StockDashboard';
import { toast } from 'sonner';

interface IntegratedStockManagementProps {
  products: Product[];
  recipes: Recipe[];
  onProductsChange: () => void;
  onRecipesChange: () => void;
}

export function IntegratedStockManagement({
  products,
  recipes,
  onProductsChange,
  onRecipesChange
}: IntegratedStockManagementProps) {
  const [showProductModal, setShowProductModal] = useState(false);
  const [showRecipeEditor, setShowRecipeEditor] = useState(false);
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleProductCreate = async (productData: any) => {
    setIsLoading(true);
    try {
      // Handle image upload if present
      let uploadedUrl = '';
      if (productData.imageFile) {
        // Implement image upload logic here
        // uploadedUrl = await uploadImage(productData.imageFile);
      }

      const response = await fetch('/api/products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...productData,
          image_url: uploadedUrl,
          updated_at: new Date().toISOString()
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      toast.success('Producto creado exitosamente');
      onProductsChange();
    } catch (error) {
      console.error('Error creating product:', error);
      toast.error(error instanceof Error ? error.message : 'Error al crear el producto');
      throw error; // Re-throw to prevent modal from closing
    } finally {
      setIsLoading(false);
    }
  };

  const handleRecipeUpdate = async (recipeId: number, updatedRecipe: Partial<Recipe>) => {
    try {
      const response = await fetch('/api/recipe', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: recipeId,
          ...updatedRecipe
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update recipe');
      }

      toast.success('Receta actualizada exitosamente');
      onRecipesChange();
    } catch (error) {
      console.error('Error updating recipe:', error);
      toast.error(error instanceof Error ? error.message : 'Error al actualizar la receta');
      throw error;
    }
  };

  const handleRecipeDelete = async (recipeId: number) => {
    try {
      const response = await fetch('/api/recipe', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id: recipeId })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete recipe');
      }

      toast.success('Receta eliminada exitosamente');
      onRecipesChange();
    } catch (error) {
      console.error('Error deleting recipe:', error);
      toast.error(error instanceof Error ? error.message : 'Error al eliminar la receta');
      throw error;
    }
  };

  const handleRecipeSelect = (recipe: Recipe) => {
    setSelectedRecipe(recipe);
    setShowRecipeEditor(true);
  };

  const handleProductSelect = (product: Product) => {
    // Handle product selection - could open product details modal
    console.log('Product selected:', product);
  };

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Gestión de Stock Integrada</h2>
        <div className="flex space-x-2">
          <Button onClick={() => setShowProductModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Producto
          </Button>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="dashboard" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dashboard">
            <BarChart3 className="h-4 w-4 mr-2" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="products">
            <Plus className="h-4 w-4 mr-2" />
            Productos
          </TabsTrigger>
          <TabsTrigger value="recipes">
            <Settings className="h-4 w-4 mr-2" />
            Recetas
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <StockDashboard 
            products={products} 
            onProductSelect={handleProductSelect}
          />
        </TabsContent>

        <TabsContent value="products" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {products.map((product) => (
              <div key={product.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium">{product.name}</h3>
                  <span className={`px-2 py-1 rounded text-xs ${
                    product.stock === 0 ? 'bg-red-100 text-red-800' :
                    product.stock < 10 ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {product.stock} unidades
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 mb-2">{product.category}</p>
                
                {product.has_recipe && (
                  <div className="text-xs text-blue-600 mb-2">
                    ✓ Tiene receta
                  </div>
                )}
                
                <div className="flex justify-between items-center text-sm">
                  <span>Precio: ${product.sale_price}</span>
                  <Button
                    onClick={() => handleProductSelect(product)}
                    size="sm"
                    variant="outline"
                  >
                    Ver Detalles
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          {products.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              <p>No hay productos registrados.</p>
              <Button 
                onClick={() => setShowProductModal(true)}
                className="mt-4"
              >
                Crear Primer Producto
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="recipes" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recipes.map((recipe) => (
              <div key={recipe.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-medium">{recipe.name}</h3>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                    {recipe.stock || 0} disponibles
                  </span>
                </div>
                
                <p className="text-sm text-gray-600 mb-2">{recipe.category}</p>
                
                <div className="text-xs text-gray-500 mb-3">
                  {recipe.ingredients?.length || 0} ingredientes
                </div>
                
                <Button
                  onClick={() => handleRecipeSelect(recipe)}
                  size="sm"
                  variant="outline"
                  className="w-full"
                >
                  Editar Receta
                </Button>
              </div>
            ))}
          </div>
          
          {recipes.length === 0 && (
            <div className="text-center py-12 text-gray-500">
              <p>No hay recetas registradas.</p>
              <p className="text-sm">Las recetas se crean automáticamente cuando creas productos con ingredientes.</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Enhanced Product Creation Modal */}
      <EnhancedProductModal
        open={showProductModal}
        onOpenChange={setShowProductModal}
        onProductCreate={handleProductCreate}
        products={products}
        recipes={recipes}
        isLoading={isLoading}
      />

      {/* Recipe Editor Modal */}
      <RecipeEditor
        open={showRecipeEditor}
        onOpenChange={setShowRecipeEditor}
        recipe={selectedRecipe}
        products={products}
        onRecipeUpdate={handleRecipeUpdate}
        onRecipeDelete={handleRecipeDelete}
      />
    </div>
  );
}
