# Enhanced Recipe and Stock Management System

## Overview

This enhanced system provides comprehensive recipe and stock management with proper ingredient tracking, stock validation, and conflict resolution. The system addresses the main issue of calculating available stock for ingredients used across multiple products.

## Key Features

### 1. Improved Stock Calculations
- **Comprehensive Stock Tracking**: Tracks total available stock, reserved stock, and actual available stock
- **Cross-Product Analysis**: Considers ingredients used in multiple products
- **Real-time Validation**: Validates stock availability before product creation

### 2. Enhanced Recipe Management
- **Existing Recipe Integration**: Use pre-defined recipes for new products
- **Custom Recipe Creation**: Create custom recipes with ingredient validation
- **Recipe Editing**: Add custom ingredients to existing recipes
- **Stock Impact Analysis**: Shows how recipe changes affect stock levels

### 3. Advanced Stock Validation
- **Multi-level Validation**: Errors, warnings, and suggestions
- **Conflict Detection**: Identifies products using similar ingredients
- **Stock Recommendations**: Provides optimization suggestions

## Components

### Core Utilities

#### `stockCalculations.ts`
- `parseIngredientQuantity()`: Parse ingredient quantities from strings
- `findMatchingProducts()`: Find products matching ingredient names
- `calculateReservedStock()`: Calculate stock reserved by existing recipes
- `getIngredientStockInfo()`: Get comprehensive stock information
- `calculateRecipeIngredientsStock()`: Calculate stock for recipe ingredients
- `validateRecipeStock()`: Validate if recipe can be created

#### `stockValidationService.ts`
- `validateProductCreation()`: Comprehensive product creation validation
- `validateRecipeUsage()`: Validate recipe usage for sales
- `getStockRecommendations()`: Get stock optimization recommendations

### UI Components

#### `RecipeIngredientManager.tsx`
- Manage recipe ingredients with real-time stock validation
- Add/remove ingredients with stock information
- Visual stock status indicators

#### `EnhancedProductModal.tsx`
- Create products with recipe integration
- Support for existing and custom recipes
- Comprehensive stock validation

#### `RecipeEditor.tsx`
- Edit existing recipes
- Add custom ingredients to recipes
- Real-time stock impact analysis

#### `StockDashboard.tsx`
- Comprehensive stock overview
- Ingredient usage analysis
- Stock recommendations and alerts

## Usage Examples

### Creating a Product with Existing Recipe

```typescript
// In your component
const handleCreateProduct = async () => {
  const productData = {
    name: "Gin Tonic Premium",
    category: "bebida",
    stock: 10,
    has_recipe: true,
    // Use existing recipe ID
    selectedRecipeId: "123"
  };
  
  await createProduct(productData);
};
```

### Creating a Product with Custom Recipe

```typescript
const customIngredients = [
  { name: "Gin Beefeater", quantity: "60", unit: "ml" },
  { name: "Tónica", quantity: "200", unit: "ml" },
  { name: "Limón", quantity: "1", unit: "slices" }
];

const productData = {
  name: "Gin Tonic Especial",
  category: "bebida", 
  stock: 5,
  has_recipe: true,
  ingredients: customIngredients
};
```

### Stock Validation Before Creation

```typescript
import { validateProductCreation } from '@/lib/stockValidationService';

const validation = await validateProductCreation(productData, allProducts);

if (!validation.canProceed) {
  console.error('Cannot create product:', validation.errors);
  return;
}

if (validation.warnings.length > 0) {
  console.warn('Warnings:', validation.warnings);
}
```

## Stock Calculation Logic

### Available Stock Calculation

For each ingredient, the system calculates:

1. **Total Available Stock**: Sum of all matching products' stock
2. **Reserved Stock**: Stock committed to existing recipes
3. **Actual Available Stock**: Total - Reserved

```typescript
// Example calculation for "Gin"
const ginProducts = findMatchingProducts("Gin", allProducts);
const totalStock = ginProducts.reduce((sum, p) => sum + p.stock, 0);
const reservedStock = calculateReservedStock("Gin", allProducts);
const actualAvailable = totalStock - reservedStock;
```

### Recipe Stock Validation

When creating a product with a recipe:

1. Calculate required ingredients for the product quantity
2. Check if actual available stock covers requirements
3. Generate errors for insufficient stock
4. Generate warnings for low stock situations

## API Integration

### Enhanced Product Creation

The `/api/products` endpoint now includes:

```typescript
// Automatic stock validation
if (body.has_recipe && body.ingredients) {
  const validation = await validateProductCreation(productData, allProducts);
  
  if (!validation.canProceed) {
    return NextResponse.json({
      error: 'Stock validation failed',
      details: validation
    }, { status: 400 });
  }
}
```

### Recipe Management

The `/api/recipe` endpoint includes:

- Enhanced ingredient validation
- Improved stock deduction logic
- Better error handling

## Best Practices

### 1. Stock Management
- Always validate stock before creating products with recipes
- Monitor stock recommendations regularly
- Keep ingredient names consistent across products

### 2. Recipe Creation
- Use descriptive ingredient names
- Specify accurate quantities and units
- Test recipes with small quantities first

### 3. Error Handling
- Handle validation errors gracefully
- Show meaningful error messages to users
- Provide suggestions for resolving issues

## Troubleshooting

### Common Issues

#### "Insufficient Stock" Error
- Check if ingredients are properly named
- Verify actual available stock vs reserved stock
- Consider reducing product quantity

#### Ingredient Not Found
- Ensure ingredient names match existing products
- Check for typos in ingredient names
- Create base ingredient products if needed

#### Stock Calculation Discrepancies
- Verify recipe ingredients are properly formatted
- Check for circular dependencies in recipes
- Ensure product stock values are accurate

## Migration Guide

### From Old System

1. **Update Imports**:
   ```typescript
   // Old
   import { calculateStock } from './oldStockUtils';
   
   // New
   import { calculateRecipeIngredientsStock } from '@/lib/stockCalculations';
   ```

2. **Replace Stock Calculations**:
   ```typescript
   // Old
   const stock = products.filter(p => p.name.includes(ingredient)).reduce(...);
   
   // New
   const stockInfo = getIngredientStockInfo(ingredient, products);
   ```

3. **Update Validation Logic**:
   ```typescript
   // Old
   if (stock < required) throw new Error('Insufficient stock');
   
   // New
   const validation = validateRecipeStock(ingredients, products, quantity);
   if (!validation.isValid) throw new Error(validation.errors.join('\n'));
   ```

## Performance Considerations

- Stock calculations are optimized for real-time use
- Ingredient matching uses efficient algorithms
- Validation results are cached where appropriate
- Large product lists are handled efficiently

## Future Enhancements

- Automatic stock reordering suggestions
- Ingredient cost optimization
- Recipe profitability analysis
- Batch recipe creation
- Integration with supplier systems
