import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Trash2, Plus, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { Product } from '@/types/types';
import { 
  calculateRecipeIngredientsStock, 
  validateRecipeStock, 
  RecipeIngredientWithStock,
  getIngredientStockInfo 
} from '@/lib/stockCalculations';

interface RecipeIngredientManagerProps {
  ingredients: Array<{ name: string; quantity: string; unit: string; productId?: string }>;
  onIngredientsChange: (ingredients: Array<{ name: string; quantity: string; unit: string; productId?: string }>) => void;
  products: Product[];
  recipeQuantity?: number;
  excludeProductId?: string;
  showStockValidation?: boolean;
}

const UNITS = ['ml', 'g', 'oz', 'cups', 'tbsp', 'tsp', 'pieces', 'slices'];

export function RecipeIngredientManager({
  ingredients,
  onIngredientsChange,
  products,
  recipeQuantity = 1,
  excludeProductId,
  showStockValidation = true
}: RecipeIngredientManagerProps) {
  const [ingredientsWithStock, setIngredientsWithStock] = useState<RecipeIngredientWithStock[]>([]);
  const [stockValidation, setStockValidation] = useState<{ isValid: boolean; errors: string[]; warnings: string[] }>({
    isValid: true,
    errors: [],
    warnings: []
  });

  // Update stock calculations when ingredients or products change
  useEffect(() => {
    if (ingredients.length > 0) {
      const stockInfo = calculateRecipeIngredientsStock(ingredients, products, recipeQuantity, excludeProductId);
      setIngredientsWithStock(stockInfo);
      
      if (showStockValidation) {
        const validation = validateRecipeStock(ingredients, products, recipeQuantity, excludeProductId);
        setStockValidation(validation);
      }
    } else {
      setIngredientsWithStock([]);
      setStockValidation({ isValid: true, errors: [], warnings: [] });
    }
  }, [ingredients, products, recipeQuantity, excludeProductId, showStockValidation]);

  const addIngredient = () => {
    const newIngredients = [...ingredients, { name: '', quantity: '', unit: 'ml' }];
    onIngredientsChange(newIngredients);
  };

  const removeIngredient = (index: number) => {
    const newIngredients = ingredients.filter((_, i) => i !== index);
    onIngredientsChange(newIngredients);
  };

  const updateIngredient = (index: number, field: string, value: string) => {
    const newIngredients = [...ingredients];
    newIngredients[index] = { ...newIngredients[index], [field]: value };
    onIngredientsChange(newIngredients);
  };

  const getStockStatusColor = (ingredient: RecipeIngredientWithStock) => {
    if (!ingredient.canFulfill) return 'destructive';
    if (ingredient.actualAvailableStock < ingredient.requiredQuantity * 2) return 'warning';
    return 'default';
  };

  const getStockStatusIcon = (ingredient: RecipeIngredientWithStock) => {
    if (!ingredient.canFulfill) return <AlertTriangle className="h-4 w-4" />;
    if (ingredient.actualAvailableStock < ingredient.requiredQuantity * 2) return <Info className="h-4 w-4" />;
    return <CheckCircle className="h-4 w-4" />;
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Ingredientes de la receta</h3>
        <Button onClick={addIngredient} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Agregar ingrediente
        </Button>
      </div>

      {/* Stock Validation Alerts */}
      {showStockValidation && stockValidation.errors.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="font-medium mb-2">Errores de stock:</div>
            <ul className="list-disc list-inside space-y-1">
              {stockValidation.errors.map((error, index) => (
                <li key={index} className="text-sm">{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {showStockValidation && stockValidation.warnings.length > 0 && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="font-medium mb-2">Advertencias:</div>
            <ul className="list-disc list-inside space-y-1">
              {stockValidation.warnings.map((warning, index) => (
                <li key={index} className="text-sm">{warning}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Ingredients List */}
      <div className="space-y-3">
        {ingredients.map((ingredient, index) => {
          const stockInfo = ingredientsWithStock[index];
          
          return (
            <Card key={index}>
              <CardContent className="pt-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-2">
                    <Label htmlFor={`ingredient-name-${index}`}>Ingrediente</Label>
                    <Input
                      id={`ingredient-name-${index}`}
                      value={ingredient.name}
                      onChange={(e) => updateIngredient(index, 'name', e.target.value)}
                      placeholder="Nombre del ingrediente"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor={`ingredient-quantity-${index}`}>Cantidad</Label>
                    <Input
                      id={`ingredient-quantity-${index}`}
                      value={ingredient.quantity}
                      onChange={(e) => updateIngredient(index, 'quantity', e.target.value)}
                      placeholder="0"
                      type="number"
                      step="0.1"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor={`ingredient-unit-${index}`}>Unidad</Label>
                    <Select
                      value={ingredient.unit}
                      onValueChange={(value) => updateIngredient(index, 'unit', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {UNITS.map(unit => (
                          <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Stock Information */}
                {showStockValidation && stockInfo && ingredient.name && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        {getStockStatusIcon(stockInfo)}
                        <span className="font-medium text-sm">Información de stock</span>
                      </div>
                      <Button
                        onClick={() => removeIngredient(index)}
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                      <div>
                        <span className="text-gray-600">Necesario:</span>
                        <div className="font-medium">{stockInfo.requiredQuantity}{stockInfo.unit}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Disponible:</span>
                        <div className="font-medium">{stockInfo.availableStock}{stockInfo.unit}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Reservado:</span>
                        <div className="font-medium">{stockInfo.reservedStock}{stockInfo.unit}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Real disponible:</span>
                        <div className={`font-medium ${stockInfo.canFulfill ? 'text-green-600' : 'text-red-600'}`}>
                          {stockInfo.actualAvailableStock}{stockInfo.unit}
                        </div>
                      </div>
                    </div>
                    
                    {!stockInfo.canFulfill && (
                      <Badge variant="destructive" className="mt-2">
                        Falta: {stockInfo.stockShortage}{stockInfo.unit}
                      </Badge>
                    )}
                  </div>
                )}

                {/* Remove button for ingredients without stock info */}
                {(!showStockValidation || !stockInfo || !ingredient.name) && (
                  <div className="flex justify-end mt-3">
                    <Button
                      onClick={() => removeIngredient(index)}
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Eliminar
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {ingredients.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No hay ingredientes agregados.</p>
          <p className="text-sm">Haz clic en "Agregar ingrediente" para comenzar.</p>
        </div>
      )}
    </div>
  );
}
