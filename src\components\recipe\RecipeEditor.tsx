import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Trash2, Plus, Edit, Save, X, AlertTriangle, Info } from 'lucide-react';
import { Product, Recipe, Ingredient } from '@/types/types';
import { RecipeIngredientManager } from './RecipeIngredientManager';
import { getIngredientStockInfo, validateRecipeStock } from '@/lib/stockCalculations';

interface RecipeEditorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  recipe: Recipe | null;
  products: Product[];
  onRecipeUpdate: (recipeId: number, updatedRecipe: Partial<Recipe>) => Promise<void>;
  onRecipeDelete: (recipeId: number) => Promise<void>;
}

export function RecipeEditor({
  open,
  onOpenChange,
  recipe,
  products,
  onRecipeUpdate,
  onRecipeDelete
}: RecipeEditorProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedRecipe, setEditedRecipe] = useState<Recipe | null>(null);
  const [newIngredients, setNewIngredients] = useState<Array<{ name: string; quantity: string; unit: string; productId?: string }>>([]);
  const [showAddIngredients, setShowAddIngredients] = useState(false);
  const [stockValidation, setStockValidation] = useState<{ isValid: boolean; errors: string[]; warnings: string[] }>({
    isValid: true,
    errors: [],
    warnings: []
  });

  // Initialize edited recipe when recipe changes
  useEffect(() => {
    if (recipe) {
      setEditedRecipe({ ...recipe });
      setNewIngredients([]);
      setShowAddIngredients(false);
      setIsEditing(false);
    }
  }, [recipe]);

  // Validate stock when recipe changes
  useEffect(() => {
    if (editedRecipe && editedRecipe.ingredients) {
      const validation = validateRecipeStock(
        editedRecipe.ingredients,
        products,
        editedRecipe.stock || 1,
        editedRecipe.id.toString()
      );
      setStockValidation(validation);
    }
  }, [editedRecipe, products]);

  const handleFieldChange = (field: keyof Recipe, value: any) => {
    if (editedRecipe) {
      setEditedRecipe({ ...editedRecipe, [field]: value });
    }
  };

  const handleIngredientChange = (index: number, field: keyof Ingredient, value: string) => {
    if (editedRecipe && editedRecipe.ingredients) {
      const updatedIngredients = [...editedRecipe.ingredients];
      updatedIngredients[index] = { ...updatedIngredients[index], [field]: value };
      setEditedRecipe({ ...editedRecipe, ingredients: updatedIngredients });
    }
  };

  const removeIngredient = (index: number) => {
    if (editedRecipe && editedRecipe.ingredients) {
      const updatedIngredients = editedRecipe.ingredients.filter((_, i) => i !== index);
      setEditedRecipe({ ...editedRecipe, ingredients: updatedIngredients });
    }
  };

  const addNewIngredientsToRecipe = () => {
    if (editedRecipe && newIngredients.length > 0) {
      const updatedIngredients = [...(editedRecipe.ingredients || []), ...newIngredients];
      setEditedRecipe({ ...editedRecipe, ingredients: updatedIngredients });
      setNewIngredients([]);
      setShowAddIngredients(false);
    }
  };

  const handleSave = async () => {
    if (!editedRecipe) return;

    try {
      await onRecipeUpdate(editedRecipe.id, {
        name: editedRecipe.name,
        ingredients: editedRecipe.ingredients,
        stock: editedRecipe.stock,
        category: editedRecipe.category
      });
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating recipe:', error);
    }
  };

  const handleDelete = async () => {
    if (!recipe) return;
    
    if (confirm('¿Estás seguro de que quieres eliminar esta receta?')) {
      try {
        await onRecipeDelete(recipe.id);
        onOpenChange(false);
      } catch (error) {
        console.error('Error deleting recipe:', error);
      }
    }
  };

  if (!recipe || !editedRecipe) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <DialogTitle>
              {isEditing ? 'Editar Receta' : 'Detalles de Receta'}
            </DialogTitle>
            <div className="flex space-x-2">
              {!isEditing ? (
                <>
                  <Button onClick={() => setIsEditing(true)} size="sm">
                    <Edit className="h-4 w-4 mr-2" />
                    Editar
                  </Button>
                  <Button onClick={handleDelete} variant="destructive" size="sm">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Eliminar
                  </Button>
                </>
              ) : (
                <>
                  <Button onClick={handleSave} size="sm" disabled={!stockValidation.isValid}>
                    <Save className="h-4 w-4 mr-2" />
                    Guardar
                  </Button>
                  <Button onClick={() => setIsEditing(false)} variant="outline" size="sm">
                    <X className="h-4 w-4 mr-2" />
                    Cancelar
                  </Button>
                </>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Recipe Information */}
          <Card>
            <CardHeader>
              <CardTitle>Información Básica</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="recipe-name">Nombre de la Receta</Label>
                  <Input
                    id="recipe-name"
                    value={editedRecipe.name}
                    onChange={(e) => handleFieldChange('name', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                
                <div>
                  <Label htmlFor="recipe-category">Categoría</Label>
                  <Select
                    value={editedRecipe.category || ''}
                    onValueChange={(value) => handleFieldChange('category', value)}
                    disabled={!isEditing}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar categoría" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bebida">Bebida</SelectItem>
                      <SelectItem value="comida">Comida</SelectItem>
                      <SelectItem value="postre">Postre</SelectItem>
                      <SelectItem value="aperitivo">Aperitivo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="recipe-stock">Stock Disponible</Label>
                  <Input
                    id="recipe-stock"
                    type="number"
                    value={editedRecipe.stock || 0}
                    onChange={(e) => handleFieldChange('stock', parseInt(e.target.value) || 0)}
                    disabled={!isEditing}
                    min="0"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stock Validation */}
          {!stockValidation.isValid && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Errores de stock:</div>
                <ul className="list-disc list-inside space-y-1">
                  {stockValidation.errors.map((error, index) => (
                    <li key={index} className="text-sm">{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {stockValidation.warnings.length > 0 && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Advertencias:</div>
                <ul className="list-disc list-inside space-y-1">
                  {stockValidation.warnings.map((warning, index) => (
                    <li key={index} className="text-sm">{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Current Ingredients */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Ingredientes Actuales</CardTitle>
                {isEditing && (
                  <Button
                    onClick={() => setShowAddIngredients(true)}
                    size="sm"
                    variant="outline"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Agregar Ingredientes
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {editedRecipe.ingredients?.map((ingredient, index) => {
                  const stockInfo = getIngredientStockInfo(ingredient.name, products, editedRecipe.id.toString());
                  
                  return (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label>Ingrediente</Label>
                          <Input
                            value={ingredient.name}
                            onChange={(e) => handleIngredientChange(index, 'name', e.target.value)}
                            disabled={!isEditing}
                          />
                        </div>
                        
                        <div>
                          <Label>Cantidad</Label>
                          <Input
                            value={ingredient.quantity}
                            onChange={(e) => handleIngredientChange(index, 'quantity', e.target.value)}
                            disabled={!isEditing}
                            type="number"
                            step="0.1"
                          />
                        </div>
                        
                        <div>
                          <Label>Unidad</Label>
                          <Select
                            value={ingredient.unit}
                            onValueChange={(value) => handleIngredientChange(index, 'unit', value)}
                            disabled={!isEditing}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {['ml', 'g', 'oz', 'cups', 'tbsp', 'tsp', 'pieces', 'slices'].map(unit => (
                                <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="flex items-end">
                          {isEditing && (
                            <Button
                              onClick={() => removeIngredient(index)}
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Stock Information */}
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                          <div>
                            <span className="text-gray-600">Disponible:</span>
                            <div className="font-medium">{stockInfo.totalAvailableStock}{ingredient.unit}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Reservado:</span>
                            <div className="font-medium">{stockInfo.totalReservedStock}{ingredient.unit}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Real disponible:</span>
                            <div className="font-medium text-green-600">{stockInfo.actualAvailableStock}{ingredient.unit}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Productos:</span>
                            <div className="font-medium">{stockInfo.matchingProducts.length}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>

              {(!editedRecipe.ingredients || editedRecipe.ingredients.length === 0) && (
                <div className="text-center py-8 text-gray-500">
                  <p>No hay ingredientes en esta receta.</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Add New Ingredients */}
          {showAddIngredients && (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Agregar Nuevos Ingredientes</CardTitle>
                  <Button
                    onClick={() => setShowAddIngredients(false)}
                    variant="ghost"
                    size="sm"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <RecipeIngredientManager
                  ingredients={newIngredients}
                  onIngredientsChange={setNewIngredients}
                  products={products}
                  recipeQuantity={editedRecipe.stock || 1}
                  excludeProductId={editedRecipe.id.toString()}
                  showStockValidation={true}
                />
                
                {newIngredients.length > 0 && (
                  <div className="flex justify-end mt-4">
                    <Button onClick={addNewIngredientsToRecipe}>
                      Agregar a la Receta
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
