import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown, 
  Package, 
  ShoppingCart,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Product } from '@/types/types';
import { getStockRecommendations } from '@/lib/stockValidationService';
import { getIngredientStockInfo } from '@/lib/stockCalculations';

interface StockDashboardProps {
  products: Product[];
  onProductSelect?: (product: Product) => void;
}

export function StockDashboard({ products, onProductSelect }: StockDashboardProps) {
  const [recommendations, setRecommendations] = useState<Array<{
    type: 'restock' | 'overstock' | 'optimize';
    productId: string;
    productName: string;
    message: string;
    priority: 'high' | 'medium' | 'low';
  }>>([]);

  // Calculate stock metrics
  const stockMetrics = useMemo(() => {
    const totalProducts = products.length;
    const lowStockProducts = products.filter(p => p.stock < 10 && p.stock > 0).length;
    const outOfStockProducts = products.filter(p => p.stock === 0).length;
    const recipeProducts = products.filter(p => p.has_recipe).length;
    const totalStockValue = products.reduce((sum, p) => sum + (p.purchase_price * p.stock), 0);
    const averageStockLevel = totalProducts > 0 ? products.reduce((sum, p) => sum + p.stock, 0) / totalProducts : 0;

    return {
      totalProducts,
      lowStockProducts,
      outOfStockProducts,
      recipeProducts,
      totalStockValue,
      averageStockLevel
    };
  }, [products]);

  // Get ingredient usage analysis
  const ingredientAnalysis = useMemo(() => {
    const ingredientUsage = new Map<string, {
      totalUsage: number;
      usedInProducts: string[];
      availableStock: number;
      reservedStock: number;
    }>();

    products.forEach(product => {
      if (product.has_recipe && product.ingredients) {
        try {
          const ingredients = JSON.parse(product.ingredients);
          ingredients.forEach((ingredient: any) => {
            const key = ingredient.name.toLowerCase();
            if (!ingredientUsage.has(key)) {
              const stockInfo = getIngredientStockInfo(ingredient.name, products);
              ingredientUsage.set(key, {
                totalUsage: 0,
                usedInProducts: [],
                availableStock: stockInfo.totalAvailableStock,
                reservedStock: stockInfo.totalReservedStock
              });
            }
            
            const usage = ingredientUsage.get(key)!;
            usage.totalUsage += parseFloat(ingredient.quantity) * product.stock;
            usage.usedInProducts.push(product.name);
          });
        } catch (error) {
          console.warn(`Failed to parse ingredients for ${product.name}`);
        }
      }
    });

    return Array.from(ingredientUsage.entries()).map(([name, data]) => ({
      name,
      ...data,
      utilizationRate: data.availableStock > 0 ? (data.reservedStock / data.availableStock) * 100 : 0
    }));
  }, [products]);

  // Update recommendations
  useEffect(() => {
    const newRecommendations = getStockRecommendations(products);
    setRecommendations(newRecommendations);
  }, [products]);

  const getStatusColor = (stock: number) => {
    if (stock === 0) return 'destructive';
    if (stock < 10) return 'warning';
    return 'default';
  };

  const getStatusIcon = (stock: number) => {
    if (stock === 0) return <XCircle className="h-4 w-4" />;
    if (stock < 10) return <AlertTriangle className="h-4 w-4" />;
    return <CheckCircle className="h-4 w-4" />;
  };

  const getPriorityColor = (priority: 'high' | 'medium' | 'low') => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'warning';
      case 'low': return 'default';
    }
  };

  return (
    <div className="space-y-6">
      {/* Stock Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Productos</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stockMetrics.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {stockMetrics.recipeProducts} con recetas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Stock Bajo</CardTitle>
            <TrendingDown className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stockMetrics.lowStockProducts}</div>
            <p className="text-xs text-muted-foreground">
              Menos de 10 unidades
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Agotados</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stockMetrics.outOfStockProducts}</div>
            <p className="text-xs text-muted-foreground">
              Sin stock disponible
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${stockMetrics.totalStockValue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Promedio: {stockMetrics.averageStockLevel.toFixed(1)} unidades
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations and Analysis */}
      <Tabs defaultValue="recommendations" className="w-full">
        <TabsList>
          <TabsTrigger value="recommendations">Recomendaciones</TabsTrigger>
          <TabsTrigger value="ingredients">Análisis de Ingredientes</TabsTrigger>
          <TabsTrigger value="alerts">Alertas de Stock</TabsTrigger>
        </TabsList>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recomendaciones de Stock</CardTitle>
            </CardHeader>
            <CardContent>
              {recommendations.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                  <p>¡Excelente! No hay recomendaciones críticas en este momento.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {recommendations.map((rec, index) => (
                    <Alert key={index} variant={getPriorityColor(rec.priority) as any}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="font-medium">{rec.productName}</div>
                            <div className="text-sm">{rec.message}</div>
                          </div>
                          <Badge variant={getPriorityColor(rec.priority) as any}>
                            {rec.priority}
                          </Badge>
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ingredients" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Análisis de Ingredientes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {ingredientAnalysis.map((ingredient, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium capitalize">{ingredient.name}</h4>
                      <Badge variant={ingredient.utilizationRate > 80 ? 'destructive' : ingredient.utilizationRate > 50 ? 'warning' : 'default'}>
                        {ingredient.utilizationRate.toFixed(1)}% utilizado
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                      <div>
                        <span className="text-gray-600">Disponible:</span>
                        <div className="font-medium">{ingredient.availableStock}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Reservado:</span>
                        <div className="font-medium">{ingredient.reservedStock}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">Uso Total:</span>
                        <div className="font-medium">{ingredient.totalUsage.toFixed(1)}</div>
                      </div>
                      <div>
                        <span className="text-gray-600">En Productos:</span>
                        <div className="font-medium">{ingredient.usedInProducts.length}</div>
                      </div>
                    </div>
                    
                    <div className="mt-2">
                      <span className="text-xs text-gray-600">Usado en: </span>
                      <span className="text-xs">{ingredient.usedInProducts.slice(0, 3).join(', ')}</span>
                      {ingredient.usedInProducts.length > 3 && (
                        <span className="text-xs text-gray-500"> y {ingredient.usedInProducts.length - 3} más</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Alertas de Stock Crítico</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {products
                  .filter(p => p.stock <= 10)
                  .sort((a, b) => a.stock - b.stock)
                  .map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(product.stock)}
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-gray-600">{product.category}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <Badge variant={getStatusColor(product.stock) as any}>
                          {product.stock} unidades
                        </Badge>
                        {onProductSelect && (
                          <Button
                            onClick={() => onProductSelect(product)}
                            size="sm"
                            variant="outline"
                          >
                            Ver Detalles
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                
                {products.filter(p => p.stock <= 10).length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                    <p>No hay productos con stock crítico.</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
