import { Product } from '@/types/types';
import { 
  calculateRecipeIngredientsStock, 
  validateRecipeStock, 
  getIngredientStockInfo,
  parseIngredientQuantity 
} from './stockCalculations';

export interface StockValidationResult {
  isValid: boolean;
  canProceed: boolean;
  errors: string[];
  warnings: string[];
  criticalIssues: string[];
  suggestions: string[];
}

export interface ProductStockImpact {
  productId: string;
  productName: string;
  currentStock: number;
  requiredStock: number;
  remainingStock: number;
  isInsufficient: boolean;
}

/**
 * Comprehensive stock validation for product creation with recipes
 */
export async function validateProductCreation(
  productData: {
    name: string;
    stock: number;
    has_recipe: boolean;
    ingredients?: Array<{ name: string; quantity: string; unit: string; productId?: string }>;
  },
  allProducts: Product[]
): Promise<StockValidationResult> {
  const result: StockValidationResult = {
    isValid: true,
    canProceed: true,
    errors: [],
    warnings: [],
    criticalIssues: [],
    suggestions: []
  };

  // If product doesn't have a recipe, no stock validation needed
  if (!productData.has_recipe || !productData.ingredients || productData.ingredients.length === 0) {
    return result;
  }

  const productStock = productData.stock || 0;
  
  // Validate each ingredient
  const ingredientValidation = validateRecipeStock(
    productData.ingredients,
    allProducts,
    productStock
  );

  result.isValid = ingredientValidation.isValid;
  result.errors = [...ingredientValidation.errors];
  result.warnings = [...ingredientValidation.warnings];

  // Additional comprehensive checks
  const stockImpacts: ProductStockImpact[] = [];

  for (const ingredient of productData.ingredients) {
    const stockInfo = getIngredientStockInfo(ingredient.name, allProducts);
    const requiredQuantity = parseIngredientQuantity(ingredient.quantity) * productStock;

    // Check for matching products
    if (stockInfo.matchingProducts.length === 0) {
      result.warnings.push(`No se encontraron productos que coincidan con "${ingredient.name}". Este ingrediente será solo informativo.`);
      continue;
    }

    // Calculate impact on each matching product
    stockInfo.matchingProducts.forEach(product => {
      const impact: ProductStockImpact = {
        productId: product.id,
        productName: product.name,
        currentStock: product.stock,
        requiredStock: requiredQuantity,
        remainingStock: product.stock - requiredQuantity,
        isInsufficient: product.stock < requiredQuantity
      };

      stockImpacts.push(impact);

      if (impact.isInsufficient) {
        result.errors.push(
          `Stock insuficiente en "${product.name}": necesario ${requiredQuantity}, disponible ${product.stock}`
        );
      } else if (impact.remainingStock < requiredQuantity) {
        result.warnings.push(
          `Stock bajo en "${product.name}": quedará ${impact.remainingStock} después de crear ${productStock} unidades de "${productData.name}"`
        );
      }
    });

    // Check for reserved stock conflicts
    if (stockInfo.totalReservedStock > 0) {
      result.warnings.push(
        `"${ingredient.name}" tiene ${stockInfo.totalReservedStock} unidades reservadas para otras recetas`
      );
    }

    // Suggest optimization
    if (stockInfo.actualAvailableStock < requiredQuantity * 2) {
      result.suggestions.push(
        `Considera reabastecer "${ingredient.name}" antes de crear grandes cantidades de "${productData.name}"`
      );
    }
  }

  // Critical issues that prevent creation
  if (result.errors.length > 0) {
    result.canProceed = false;
    result.criticalIssues.push(
      `No se puede crear el producto "${productData.name}" debido a stock insuficiente en los ingredientes`
    );
  }

  // Check for potential conflicts with existing recipes
  const conflictingProducts = findConflictingProducts(productData, allProducts);
  if (conflictingProducts.length > 0) {
    result.warnings.push(
      `Los siguientes productos también usan ingredientes similares: ${conflictingProducts.map(p => p.name).join(', ')}`
    );
  }

  return result;
}

/**
 * Validate stock before making a sale or using a recipe
 */
export async function validateRecipeUsage(
  recipeId: string,
  quantity: number,
  allProducts: Product[]
): Promise<StockValidationResult> {
  const result: StockValidationResult = {
    isValid: true,
    canProceed: true,
    errors: [],
    warnings: [],
    criticalIssues: [],
    suggestions: []
  };

  const recipe = allProducts.find(p => p.id === recipeId && p.has_recipe);
  if (!recipe || !recipe.ingredients) {
    result.errors.push('Receta no encontrada o no tiene ingredientes');
    result.isValid = false;
    result.canProceed = false;
    return result;
  }

  let ingredients;
  try {
    ingredients = JSON.parse(recipe.ingredients);
  } catch (error) {
    result.errors.push('Error al procesar los ingredientes de la receta');
    result.isValid = false;
    result.canProceed = false;
    return result;
  }

  // Validate stock for the requested quantity
  const validation = validateRecipeStock(ingredients, allProducts, quantity, recipeId);
  
  result.isValid = validation.isValid;
  result.canProceed = validation.isValid;
  result.errors = validation.errors;
  result.warnings = validation.warnings;

  if (!validation.isValid) {
    result.criticalIssues.push(
      `No se puede usar ${quantity} unidades de "${recipe.name}" debido a stock insuficiente`
    );
  }

  return result;
}

/**
 * Find products that might conflict with the new product's ingredient usage
 */
function findConflictingProducts(
  newProduct: { ingredients?: Array<{ name: string; quantity: string; unit: string }> },
  allProducts: Product[]
): Product[] {
  if (!newProduct.ingredients) return [];

  const conflictingProducts: Product[] = [];
  const newIngredientNames = newProduct.ingredients.map(ing => ing.name.toLowerCase());

  allProducts.forEach(product => {
    if (!product.has_recipe || !product.ingredients) return;

    try {
      const productIngredients = JSON.parse(product.ingredients);
      const hasCommonIngredients = productIngredients.some((ing: any) =>
        newIngredientNames.some(newIng => 
          ing.name.toLowerCase().includes(newIng) || newIng.includes(ing.name.toLowerCase())
        )
      );

      if (hasCommonIngredients) {
        conflictingProducts.push(product);
      }
    } catch (error) {
      console.warn(`Failed to parse ingredients for product ${product.name}`);
    }
  });

  return conflictingProducts;
}

/**
 * Get stock recommendations for optimal inventory management
 */
export function getStockRecommendations(
  products: Product[]
): Array<{
  type: 'restock' | 'overstock' | 'optimize';
  productId: string;
  productName: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
}> {
  const recommendations: Array<{
    type: 'restock' | 'overstock' | 'optimize';
    productId: string;
    productName: string;
    message: string;
    priority: 'high' | 'medium' | 'low';
  }> = [];

  products.forEach(product => {
    // Check for low stock
    if (product.stock < 10 && product.stock > 0) {
      recommendations.push({
        type: 'restock',
        productId: product.id,
        productName: product.name,
        message: `Stock bajo: ${product.stock} unidades restantes`,
        priority: 'high'
      });
    }

    // Check for out of stock
    if (product.stock === 0) {
      recommendations.push({
        type: 'restock',
        productId: product.id,
        productName: product.name,
        message: 'Producto agotado',
        priority: 'high'
      });
    }

    // Check for overstock (more than 100 units for non-recipe products)
    if (!product.has_recipe && product.stock > 100) {
      recommendations.push({
        type: 'overstock',
        productId: product.id,
        productName: product.name,
        message: `Posible sobrestock: ${product.stock} unidades`,
        priority: 'low'
      });
    }

    // Check for recipe products with ingredients that might be running low
    if (product.has_recipe && product.ingredients) {
      try {
        const ingredients = JSON.parse(product.ingredients);
        ingredients.forEach((ingredient: any) => {
          const stockInfo = getIngredientStockInfo(ingredient.name, products);
          if (stockInfo.actualAvailableStock < parseIngredientQuantity(ingredient.quantity) * 5) {
            recommendations.push({
              type: 'optimize',
              productId: product.id,
              productName: product.name,
              message: `Ingrediente "${ingredient.name}" con stock bajo para esta receta`,
              priority: 'medium'
            });
          }
        });
      } catch (error) {
        console.warn(`Failed to analyze ingredients for ${product.name}`);
      }
    }
  });

  return recommendations.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    return priorityOrder[b.priority] - priorityOrder[a.priority];
  });
}
